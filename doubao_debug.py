"""
豆包AI图像生成按钮调试脚本
专门用于测试和调试不同的元素定位策略
"""

import time
from selenium import webdriver
from selenium.webdriver.edge.service import Service
from selenium.webdriver.edge.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC


def setup_driver():
    """设置浏览器驱动"""
    try:
        edge_options = Options()
        edge_options.add_argument("--disable-blink-features=AutomationControlled")
        edge_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        edge_options.add_experimental_option('useAutomationExtension', False)
        
        driver = webdriver.Edge(options=edge_options)
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        driver.maximize_window()
        
        return driver
    except Exception as e:
        print(f"设置驱动失败: {e}")
        return None


def analyze_page_structure(driver):
    """分析页面结构，查找所有可能的图像生成按钮"""
    print("\n" + "="*80)
    print("页面结构分析")
    print("="*80)

    # 基本页面信息
    print(f"页面标题: {driver.title}")
    print(f"当前URL: {driver.current_url}")

    # 等待页面完全加载
    time.sleep(5)

    # 查找所有可能相关的元素
    selectors_to_check = [
        ("技能栏相关", "//div[contains(@class, 'skill')]"),
        ("data-testid属性", "//div[@data-testid]"),
        ("技能按钮", "//div[contains(@class, 'skill-bar-button')]"),
        ("按钮相关", "//div[contains(@class, 'button') or contains(@class, 'btn')]"),
        ("role=button", "//div[@role='button']"),
        ("包含'图'字的元素", "//div[contains(text(), '图')]"),
        ("包含'生成'的元素", "//div[contains(text(), '生成')]"),
        ("包含'画'字的元素", "//div[contains(text(), '画')]"),
        ("所有可点击div", "//div[@onclick or @role='button']"),
    ]
    
    for desc, selector in selectors_to_check:
        try:
            elements = driver.find_elements(By.XPATH, selector)
            print(f"\n{desc} ({len(elements)} 个元素):")
            
            for i, elem in enumerate(elements[:3]):  # 只显示前3个
                try:
                    text = elem.text.strip()
                    class_name = elem.get_attribute('class') or ''
                    data_testid = elem.get_attribute('data-testid') or ''
                    onclick = elem.get_attribute('onclick') or ''
                    
                    print(f"  {i+1}. 文本: '{text[:30]}'")
                    print(f"     类名: '{class_name[:50]}'")
                    if data_testid:
                        print(f"     data-testid: '{data_testid}'")
                    if onclick:
                        print(f"     onclick: '{onclick[:30]}'")
                    print()
                except Exception as e:
                    print(f"     获取元素信息失败: {e}")
                    
        except Exception as e:
            print(f"{desc}: 查找失败 - {e}")
    
    print("="*80)

    # 专门分析技能栏
    analyze_skill_bar(driver)


def analyze_skill_bar(driver):
    """专门分析技能栏结构"""
    print("\n" + "="*60)
    print("技能栏详细分析")
    print("="*60)

    try:
        # 查找技能栏容器
        skill_bar_selectors = [
            "//div[contains(@class, 'skill-bar')]",
            "//div[contains(@class, 'skill')]",
            "//div[contains(@data-testid, 'skill')]"
        ]

        skill_bar = None
        for selector in skill_bar_selectors:
            try:
                elements = driver.find_elements(By.XPATH, selector)
                if elements:
                    skill_bar = elements[0]
                    print(f"找到技能栏: {selector}")
                    break
            except:
                continue

        if not skill_bar:
            print("❌ 未找到技能栏")
            return

        # 分析技能栏中的所有按钮
        button_selectors = [
            ".//div[contains(@class, 'skill-bar-button')]",
            ".//div[@data-testid]",
            ".//div[@role='button']",
            ".//div[contains(@class, 'button')]"
        ]

        all_buttons = []
        for selector in button_selectors:
            try:
                buttons = skill_bar.find_elements(By.XPATH, selector)
                for button in buttons:
                    if button not in all_buttons:
                        all_buttons.append(button)
            except:
                continue

        print(f"技能栏中找到 {len(all_buttons)} 个按钮:")

        for i, button in enumerate(all_buttons):
            try:
                text = button.text.strip()
                class_name = button.get_attribute('class') or ''
                data_testid = button.get_attribute('data-testid') or ''
                is_displayed = button.is_displayed()
                is_enabled = button.is_enabled()

                print(f"\n按钮 {i+1}:")
                print(f"  文本: '{text}'")
                print(f"  类名: '{class_name}'")
                print(f"  data-testid: '{data_testid}'")
                print(f"  可见: {is_displayed}, 可用: {is_enabled}")

                # 判断是否可能是图像生成按钮
                keywords = ['图像', '图片', '画图', 'image', 'generate', 'draw']
                is_likely = any(keyword in text.lower() or
                              keyword in class_name.lower() or
                              keyword in data_testid.lower()
                              for keyword in keywords)

                if is_likely:
                    print(f"  ⭐ 这个按钮很可能是图像生成按钮！")

            except Exception as e:
                print(f"  分析按钮 {i+1} 时出错: {e}")

        print("="*60)

    except Exception as e:
        print(f"分析技能栏时出错: {e}")


def test_click_strategies(driver):
    """测试不同的点击策略"""
    print("\n开始测试点击策略...")

    # 定义多种定位策略（按优先级排序）
    strategies = [
        ("data-testid='skill_bar_button_3'", "//div[@data-testid='skill_bar_button_3']"),
        ("data-testid='skill_bar_button_2'", "//div[@data-testid='skill_bar_button_2']"),
        ("技能栏第3个按钮", "(//div[contains(@class, 'skill-bar-button')])[3]"),
        ("技能栏第4个按钮", "(//div[contains(@class, 'skill-bar-button')])[4]"),
        ("文本包含'图像'", "//div[contains(text(), '图像')]"),
        ("文本包含'图片'", "//div[contains(text(), '图片')]"),
        ("文本包含'画图'", "//div[contains(text(), '画图')]"),
        ("class包含'skill-bar-button'", "//div[contains(@class, 'skill-bar-button')]"),
        ("data-testid包含'skill_bar_button'", "//div[contains(@data-testid, 'skill_bar_button')]"),
        ("role=button且包含图", "//div[@role='button' and contains(., '图')]"),
    ]
    
    wait = WebDriverWait(driver, 5)
    
    for desc, selector in strategies:
        try:
            print(f"\n测试策略: {desc}")
            print(f"选择器: {selector}")
            
            elements = driver.find_elements(By.XPATH, selector)
            print(f"找到 {len(elements)} 个匹配元素")
            
            if elements:
                for i, elem in enumerate(elements):
                    try:
                        text = elem.text.strip()
                        is_displayed = elem.is_displayed()
                        is_enabled = elem.is_enabled()
                        
                        print(f"  元素 {i+1}: '{text}' (显示: {is_displayed}, 启用: {is_enabled})")
                        
                        if is_displayed and is_enabled and ('图' in text or 'image' in text.lower()):
                            print(f"  ✅ 这个元素看起来很有希望！")
                            
                            # 询问是否尝试点击
                            try:
                                user_input = input(f"    是否尝试点击这个元素？(y/n): ").lower()
                                if user_input == 'y':
                                    # 滚动到元素位置
                                    driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", elem)
                                    time.sleep(1)
                                    
                                    # 尝试点击
                                    elem.click()
                                    print("    ✅ 点击成功！")
                                    time.sleep(2)
                                    
                                    # 检查是否有变化
                                    current_url = driver.current_url
                                    print(f"    当前URL: {current_url}")
                                    return True
                            except KeyboardInterrupt:
                                print("    用户取消操作")
                                return False
                            except Exception as click_error:
                                print(f"    点击失败: {click_error}")
                                try:
                                    driver.execute_script("arguments[0].click();", elem)
                                    print("    ✅ JavaScript点击成功！")
                                    time.sleep(2)
                                    return True
                                except Exception as js_error:
                                    print(f"    JavaScript点击也失败: {js_error}")
                    except Exception as e:
                        print(f"  处理元素时出错: {e}")
                        
        except Exception as e:
            print(f"策略 '{desc}' 失败: {e}")
    
    return False


def main():
    """主函数"""
    print("豆包AI图像生成按钮调试工具")
    print("="*50)
    
    driver = setup_driver()
    if not driver:
        print("无法设置浏览器驱动")
        return
    
    try:
        # 打开豆包网站
        print("正在打开豆包AI网站...")
        driver.get("https://www.doubao.com")
        
        # 等待页面加载
        print("等待页面加载...")
        time.sleep(10)
        
        # 分析页面结构
        analyze_page_structure(driver)
        
        # 询问用户是否继续测试点击
        try:
            user_input = input("\n是否继续测试点击策略？(y/n): ").lower()
            if user_input == 'y':
                test_click_strategies(driver)
        except KeyboardInterrupt:
            print("\n用户中断操作")
        
        # 保持浏览器打开
        input("\n按Enter键关闭浏览器...")
        
    except Exception as e:
        print(f"发生错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        driver.quit()
        print("浏览器已关闭")


if __name__ == "__main__":
    main()
