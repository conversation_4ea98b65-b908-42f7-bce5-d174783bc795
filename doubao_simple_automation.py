"""
豆包AI海报生成自动化脚本
使用Playwright实现更可靠的自动化操作
"""

import webbrowser
import time
import os
from pathlib import Path

# Excel读取功能 - 延迟检查，在需要时再检查
PANDAS_AVAILABLE = False
OPENPYXL_AVAILABLE = False

def check_excel_libraries():
    """检查Excel读取库的可用性"""
    global PANDAS_AVAILABLE, OPENPYXL_AVAILABLE

    try:
        import pandas as pd
        PANDAS_AVAILABLE = True
    except ImportError:
        PANDAS_AVAILABLE = False

    try:
        import openpyxl
        OPENPYXL_AVAILABLE = True
    except ImportError:
        OPENPYXL_AVAILABLE = False

    return PANDAS_AVAILABLE or OPENPYXL_AVAILABLE

# Selenium作为备用方案
try:
    from selenium import webdriver
    from selenium.webdriver.edge.service import Service
    from selenium.webdriver.edge.options import Options
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.common.exceptions import TimeoutException, WebDriverException
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False

# Playwright作为主要方案
try:
    from playwright.sync_api import sync_playwright
    PLAYWRIGHT_AVAILABLE = True
except ImportError:
    PLAYWRIGHT_AVAILABLE = False


class DoubaoSimpleAutomation:
    def __init__(self):
        """初始化自动化类"""
        self.url = "https://www.doubao.com"
        self.driver = None
        self.wait = None
        self.login_button_xpath = "/html/body/div[1]/div[1]/div/div[3]/div/main/div/div/div[1]/div/div/div/div[2]/div/button"

        # 登录流程相关的XPath
        self.phone_input_xpath = "/html/body/div[5]/div/div[2]/div/div/div/div/div/div/div/div[2]/div[2]/div[1]/div/div[2]/input"
        self.agreement_checkbox_xpath = "/html/body/div[5]/div/div[2]/div/div/div/div/div/div/div/div[2]/div[2]/div[2]/span/span/span"
        self.login_submit_xpath = "/html/body/div[5]/div/div[2]/div/div/div/div/div/div/div/div[2]/div[2]/div[1]/button"
        self.phone_number = "19942211213"

        # 登录后要点击的元素
        self.after_login_click_xpath = "/html/body/div[1]/div[1]/div/div[3]/div/main/div/div/div[2]/div/div/div[2]/div[3]/div/div/div/div/div[2]/div[1]/div"

        # 海报生成相关元素
        self.poster_input_xpath = "/html/body/div[1]/div[1]/div/div[3]/div/main/div/div/div[2]/div/div/div[2]/div[2]/div[2]/div/div/div[2]/div[1]/div/div/div"
        self.excel_file_path = r"D:\Cache\WPS\可组合实体词.xlsx"

        # 最终提交按钮
        self.final_submit_button_xpath = "/html/body/div[1]/div[1]/div/div[3]/div/main/div/div/div[2]/div/div/div[2]/div[2]/div[2]/div/div/div[2]/div[2]/div[3]/div/div[3]/div[2]/button"

        # 第二轮生成相关元素
        self.generation_status_xpath = "/html/body/div[1]/div[1]/div/div[3]/div/main/div/div/div[3]/div/div/div/div[3]/div/div/div/div[2]/div[2]/div[3]/div/div[3]/div[1]"
        self.second_input_xpath = "/html/body/div[1]/div[1]/div/div[3]/div/main/div/div/div[3]/div/div/div/div[3]/div/div/div/div[2]/div[1]/div[1]/div/div"
        self.second_submit_xpath = "/html/body/div[1]/div[1]/div/div[3]/div/main/div/div/div[3]/div/div/div/div[3]/div/div/div/div[2]/div[2]/div[3]/div/div[3]/div[2]/button"
        self.second_command = '不要出现"858""85""48""36""3648""95""40""8""98""386""340"等数字，结合实际，丰富背景，字数多点，请你优化，请你重新生成8张不同的'

        # 第三轮生成相关元素（使用相同的XPath）
        self.third_input_xpath = "/html/body/div[1]/div[1]/div/div[3]/div/main/div/div/div[3]/div/div/div/div[3]/div/div/div/div[2]/div[1]/div[1]/div/div"
        self.third_submit_xpath = "/html/body/div[1]/div[1]/div/div[3]/div/main/div/div/div[3]/div/div/div/div[3]/div/div/div/div[2]/div[2]/div[3]/div/div[3]/div[2]/button"
        self.third_command = '背景丰富点而且要真实，符合实际，网格类型不要太明显，背景都优化换一下并且优化，文字清晰一点，文字多一点，留白不要太严重，整体及文字排版修改优化一下，请你重新生成8张不同的给我'

        # 第四轮生成相关元素（使用相同的XPath）
        self.fourth_input_xpath = "/html/body/div[1]/div[1]/div/div[3]/div/main/div/div/div[3]/div/div/div/div[3]/div/div/div/div[2]/div[1]/div[1]/div/div"
        self.fourth_submit_xpath = "/html/body/div[1]/div[1]/div/div[3]/div/main/div/div/div[3]/div/div/div/div[3]/div/div/div/div[2]/div[2]/div[3]/div/div[3]/div[2]/button"
        self.fourth_command = '整体还可以优化，背景要丰富真实，结合实际，文字部分还可以增加，请你再来8张不同的'

        # 循环优化生成相关元素（使用相同的XPath）
        self.loop_input_xpath = "/html/body/div[1]/div[1]/div/div[3]/div/main/div/div/div[3]/div/div/div/div[3]/div/div/div/div[2]/div[1]/div[1]/div/div"
        self.loop_submit_xpath = "/html/body/div[1]/div[1]/div/div[3]/div/main/div/div/div[3]/div/div/div/div[3]/div/div/div/div[2]/div[2]/div[3]/div/div[3]/div[2]/button"
        self.loop_command = '整体效果还可以，但是布局还有部分图片留白还可以优化，请你重新生成8张不同的给我'
        self.loop_count = 8  # 重复次数

        # 最终操作相关元素
        self.final_button1_xpath = "/html/body/div[1]/div[1]/div/div[3]/div/main/div/div/div[2]/div/div[1]/div/div/div[2]/div[10]/div/div/div/div/div/div/div[2]/div/div/div/button[3]"
        self.final_button2_xpath = "/html/body/div[1]/div[1]/div/div[3]/div/main/div/div/div[3]/div/div/div/div/div/span"
        self.final_button3_xpath = "/html/body/div[1]/div[1]/div/div[3]/div/main/div/div/div[3]/div/div/div/div/button"
        self.final_button4_xpath = "/html/body/div[1]/div[1]/div/div[3]/div/nav/aside/div/div[2]/div"

        # Edge浏览器路径
        self.edge_paths = [
            r"C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe",
            r"C:\Program Files\Microsoft\Edge\Application\msedge.exe",
        ]

    def setup_selenium_driver(self):
        """设置Selenium驱动（用于点击操作）"""
        try:
            print("正在设置Selenium驱动...")
            # 配置Edge浏览器选项
            edge_options = Options()
            edge_options.add_argument("--disable-blink-features=AutomationControlled")
            edge_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            edge_options.add_experimental_option('useAutomationExtension', False)

            # 尝试多种方式创建Edge驱动实例
            driver_paths = [
                "./drivers/msedgedriver.exe",  # 本地下载的驱动
                "msedgedriver.exe",  # 系统PATH中的驱动
                None  # 让Selenium自动查找
            ]

            for driver_path in driver_paths:
                try:
                    if driver_path and os.path.exists(driver_path):
                        print(f"尝试使用本地驱动: {driver_path}")
                        service = Service(driver_path)
                        self.driver = webdriver.Edge(service=service, options=edge_options)
                    else:
                        print("尝试使用系统默认驱动...")
                        self.driver = webdriver.Edge(options=edge_options)

                    self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
                    self.wait = WebDriverWait(self.driver, 10)
                    self.driver.maximize_window()
                    print("✅ Selenium驱动设置成功！")
                    return True

                except Exception as e:
                    print(f"驱动路径 {driver_path} 失败: {e}")
                    continue

            print("❌ 所有驱动路径都失败了")
            return False

        except Exception as e:
            print(f"设置Selenium驱动时发生错误: {e}")
            return False

    def find_locator_any_frame(self, page, xpath):
        """在页面或任何frame中查找元素（参考ZDHTool.py）"""
        loc = page.locator(f"xpath={xpath}")
        if loc.count():
            return loc
        for fr in page.frames:
            try:
                loc2 = fr.locator(f"xpath={xpath}")
                if loc2.count():
                    return loc2
            except Exception:
                continue
        return page.locator(f"xpath={xpath}")

    def read_excel_data(self, row_number=1):
        """读取Excel文件指定行的A列数据"""
        print(f"正在尝试读取Excel文件: {self.excel_file_path}, 行号: A{row_number}")

        # 检查文件是否存在
        if not os.path.exists(self.excel_file_path):
            print(f"❌ Excel文件不存在: {self.excel_file_path}")
            return None

        # 动态检查库的可用性
        check_excel_libraries()

        # 方法1: 尝试使用pandas
        if PANDAS_AVAILABLE:
            try:
                print("尝试使用pandas读取Excel...")
                import pandas as pd
                df = pd.read_excel(self.excel_file_path, header=None)

                if df.empty or row_number > len(df) or df.iloc[row_number-1, 0] is None:
                    print(f"❌ A{row_number}单元格为空或不存在")
                    return None

                cell_value = str(df.iloc[row_number-1, 0]).strip()
                print(f"✅ 使用pandas成功读取A{row_number}单元格数据: {cell_value}")
                return cell_value

            except Exception as e:
                print(f"❌ pandas读取失败: {e}")

        # 方法2: 尝试使用openpyxl
        if OPENPYXL_AVAILABLE:
            try:
                print("尝试使用openpyxl读取Excel...")
                from openpyxl import load_workbook
                workbook = load_workbook(self.excel_file_path)
                worksheet = workbook.active
                cell_value = worksheet[f'A{row_number}'].value

                if cell_value is None:
                    print(f"❌ A{row_number}单元格为空")
                    return None

                cell_value = str(cell_value).strip()
                print(f"✅ 使用openpyxl成功读取A{row_number}单元格数据: {cell_value}")
                return cell_value

            except Exception as e:
                print(f"❌ openpyxl读取失败: {e}")

        # 方法3: 手动安装pandas并重试
        print("尝试自动安装pandas和openpyxl...")
        try:
            import subprocess
            import sys

            # 安装pandas和openpyxl
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pandas", "openpyxl"])
            print("✅ 成功安装pandas和openpyxl")

            # 重新导入pandas
            import pandas as pd
            df = pd.read_excel(self.excel_file_path, header=None)

            if df.empty or row_number > len(df) or df.iloc[row_number-1, 0] is None:
                print(f"❌ A{row_number}单元格为空或不存在")
                return None

            cell_value = str(df.iloc[row_number-1, 0]).strip()
            print(f"✅ 安装后成功读取A{row_number}单元格数据: {cell_value}")
            return cell_value

        except Exception as e:
            print(f"❌ 自动安装和读取失败: {e}")

        print("❌ 所有Excel读取方法都失败了")
        return None

    def generate_poster_prompt(self, entity_word):
        """生成海报提示词"""
        base_prompt = f"""帮我生成八张{{{entity_word}}}活动海报，比例是9：16。具体每张构图需求和文字大小你可以选择的如下，海报需要有相应的量的文字进行解释，并且将文字解释放出进去之前，请先输出你要放入的文字，再将其放进图片。并且功能介绍需要大概30词左右，其字体字号是40号字体，功能介绍要放进海报当中：

一、 1. 中心对称式 (经典稳重，聚焦核心) 背景图： 置于海报正中央，占据较大面积（约60%-80%）。可以是产品、人物肖像、核心场景。背景尽量简洁或虚化，突出主体。 文字： 主标题： 直接叠加在主体图上方或下方中央位置。字体较大、粗壮、醒目。 副标题/关键信息： 放在主标题下方或上方，字体稍小，与主标题形成对比。 其他信息（时间、地点、口号等）： 放在海报底部或顶部边缘，居中对齐。字体最小。 大小比例： 主图 > 主标题 > 副标题/关键信息 > 其他信息。 2. 上下分割式 (清晰分区，对比鲜明) 背景图： 占据海报的上半部分（约50%-70%）或下半部分。上半部放图更常见（符合视觉习惯）。图片内容需与下方文字区有明确关联或对比。 文字： 主标题： 通常放在图片和文字区的交界处（压在图底或文字区顶部），或大胆地置于图片区域内（确保可读性）。字体大且醒目。 其他信息： 集中放在下半部分的纯色或简约纹理背景区域。可以左对齐、右对齐或居中对齐排版。 大小比例： 图片区 ≈ 文字区（比例可调，如6：4）。主标题是文字区的核心，字体最大。 3. 左右分割式 (现代感强，图文并置) 背景图： 占据海报左侧或右侧（约40%-60%）。选择有视觉冲击力或能传达核心信息的图片。 文字： 主标题： 放在另一侧区域的显著位置（顶部或中部偏上）。字体要足够大，与图片形成平衡。 副标题/正文： 在主标题下方区域排版。注意行距和段落间距。 其他信息： 放在文字区的底部或角落。 大小比例： 图片区宽度 ≈ 文字区宽度。主标题是文字区的视觉焦点。 4. 对角线构图 (动态活力，引导视线) - 你提到的例子，再深化 背景图： 方式一： 主体图本身呈对角线方向摆放（如倾斜的产品、奔跑的人物）。 方式二： 利用色块、线条、或图片的延伸感形成对角线的视觉引导。 文字： 主标题： 沿着对角线的方向排列，或放在对角线的一端/焦点处。可以倾斜排版（角度不宜过大，保证易读性）或保持水平但位置在对角线上。 其他信息： 可以放在对角线的另一端、海报的角落或空白处，保持水平排版以维持稳定感。 大小比例： 主标题通常较大且醒目，其他信息较小。倾斜的元素是视觉重心。 5. 满版出血式 (沉浸感强，视觉震撼) 背景图： 铺满整个海报，不留白边（"出血"设计）。图片质量必须极高，有足够的视觉张力和故事性。 文字： 主标题： 直接叠加在图片上。位置是关键！ 常放在： 视觉中心点： 最核心位置。 留白/纯净区域： 如天空、水面、纯色背景处。 黄金分割点： 约1/3或2/3处。 文字处理： 字体要非常醒目（粗体、大字号、高对比色或加描边/阴影确保可读性）。文字区域可加半透明色块衬底（谨慎使用，避免破坏图片感）。 其他信息： 放在海报边缘（底部居多）或图片中相对简洁的区域。 大小比例： 图片是绝对主角。文字大小需在震撼和可读性间取得平衡。 6. 框架式构图 (聚焦中心，增加层次) 背景图： 方式一： 核心主体图被文字、色块或辅助图形（如边框、线条）形成的"框"所包围。 方式二： 利用前景元素（如窗户、门洞、树枝、人物剪影）自然形成画框，框住后方的主体或文字。 文字： 主标题： 通常放在"框"内（与主体结合）或作为"框"的一部分（如环绕文字）。 其他信息： 放在框外边缘（如底部、顶部），或者巧妙地融入边框设计中。 大小比例： "框"内的主体/主标题是核心。框本身不宜过重以免抢戏。 7. 放射式构图 (能量爆发，聚焦中心) 背景图： 主体位于中心点。 背景设计（线条、光束、色块、图形元素、甚至人群/物体）呈放射状从中心向外扩散。 文字： 主标题： 放在绝对中心（覆盖主体或与主体结合），或沿着主要放射线排列。 其他信息： 放在放射线的末端、海报边缘或相对稳定的区域（如底部居中）。 大小比例： 中心点是绝对焦点，元素大小可能随着放射线变化。 放射状颜色不要和文字颜色重复使得文字看不清楚 8. 网格系统 (秩序严谨，灵活多变) 背景图： 图片作为网格中的一个或多个模块。可以是单张大图占据多个格子，或多张小图按网格排列。 文字： 主标题： 通常占据一个或多个显眼的网格单元（如顶部居中、左上角大单元）。可以跨网格。 其他信息： 严格按照网格划分的区域进行排版，确保对齐（左/右/顶/底对齐）。不同层级信息占据不同大小/位置的格子。 大小比例： 网格定义了比例关系。主标题所在的格子通常是最大的或位置最重要的。

二、文字解释要中文，文字需要清晰，不要扭曲，不要虚化不清楚，对于海报字体的大小，可以参考以下要求，文字不要细体字，都需要粗体字，各字体字号设置如下： 主标题使用85号字体，以确保在远处可见。 85不要出现在海报中。 副标题使用36号字体。 36不要出现在海报中。 正文内容使用48号字体，确保信息清晰易读，不要虚化不清楚。 任意数字和数字的组合，比如比例数字、时钟都不能出现在海报中

三、其他需求如下： 海报的文字和产品协调性要求如下： 信息可读性与美观：信息层级分明（标题/正文大小对比明确），关键信息一目了然；布局符合阅读动线。创新性排版增强信息传达（如动态字体、空间切割），信息密度与易读性完美平衡 视觉冲击力 ：如色彩搭配、图像质量、视觉焦点：通过超现实合成、动态光影等手段创造强情绪张力，视觉焦点瞬间抓人 布局平衡 ：评估元素布局、负空间使用和视觉引导，如标题、图像、文字的分布是否协调，确保信息层次清晰。：突破传统构图（如负空间反转、多焦点动态平衡），营造独特节奏感 图像与文字整合：评估图像和文字的结合是否无缝，如文字位置不影响图像焦点，整体视觉流畅。：文字与图像互动设计精妙（如文字成为图像的一部分），空间透视一致，达到视觉叙事效果"""

        return base_prompt

    def wait_for_generation_complete(self, page, timeout=600):
        """等待海报生成完成"""
        print("开始监控生成状态...")
        start_time = time.time()

        while time.time() - start_time < timeout:
            try:
                # 查找状态元素
                status_element = self.find_locator_any_frame(page, self.generation_status_xpath)

                # 获取class属性
                class_value = status_element.get_attribute("class")
                print(f"当前状态class: {class_value}")

                # 检查是否包含 !hidden，表示生成完成
                if "!hidden" in class_value:
                    print("✅ 检测到生成完成标志（!hidden）")
                    return True
                elif "break-btn-Rv8NnA" in class_value and "!hidden" not in class_value:
                    print("🔄 正在生成中...")

                # 每5秒检查一次
                time.sleep(5)

            except Exception as e:
                print(f"监控状态时出错: {e}")
                time.sleep(5)
                continue

        print("❌ 等待生成完成超时")
        return False

    def execute_second_generation(self, page):
        """执行第二轮生成"""
        try:
            print("开始执行第二轮生成...")

            # 步骤1: 等待生成完成
            if not self.wait_for_generation_complete(page):
                print("❌ 第一轮生成未完成，跳过第二轮")
                return False

            # 步骤2: 额外等待30秒保险
            print("步骤2: 额外等待30秒...")
            time.sleep(30)

            # 步骤3: 点击第二个输入框
            print("步骤3: 查找并点击第二个输入框...")
            second_input = self.find_locator_any_frame(page, self.second_input_xpath)
            second_input.wait_for(state="visible", timeout=15000)
            second_input.click()
            print("✅ 成功点击第二个输入框！")

            # 步骤4: 输入第二条命令
            print("步骤4: 输入第二条命令...")
            second_input.fill(self.second_command)
            print("✅ 成功输入第二条命令！")

            # 步骤5: 点击第二个提交按钮
            print("步骤5: 查找并点击第二个提交按钮...")
            time.sleep(2)  # 等待输入完成
            second_submit = self.find_locator_any_frame(page, self.second_submit_xpath)
            second_submit.wait_for(state="visible", timeout=15000)
            second_submit.click(force=True)
            print("✅ 成功点击第二个提交按钮！")

            print("✅ 第二轮生成流程执行完成！")
            return True

        except Exception as e:
            print(f"❌ 第二轮生成执行失败: {e}")
            return False

    def execute_third_generation(self, page):
        """执行第三轮生成"""
        try:
            print("开始执行第三轮生成...")

            # 步骤1: 等待第二轮生成完成
            if not self.wait_for_generation_complete(page):
                print("❌ 第二轮生成未完成，跳过第三轮")
                return False

            # 步骤2: 额外等待30秒保险
            print("步骤2: 额外等待30秒...")
            time.sleep(30)

            # 步骤3: 点击第三个输入框
            print("步骤3: 查找并点击第三个输入框...")
            third_input = self.find_locator_any_frame(page, self.third_input_xpath)
            third_input.wait_for(state="visible", timeout=15000)
            third_input.click()
            print("✅ 成功点击第三个输入框！")

            # 步骤4: 输入第三条命令
            print("步骤4: 输入第三条命令...")
            third_input.fill(self.third_command)
            print("✅ 成功输入第三条命令！")

            # 步骤5: 点击第三个提交按钮
            print("步骤5: 查找并点击第三个提交按钮...")
            time.sleep(2)  # 等待输入完成
            third_submit = self.find_locator_any_frame(page, self.third_submit_xpath)
            third_submit.wait_for(state="visible", timeout=15000)
            third_submit.click(force=True)
            print("✅ 成功点击第三个提交按钮！")

            print("✅ 第三轮生成流程执行完成！")
            return True

        except Exception as e:
            print(f"❌ 第三轮生成执行失败: {e}")
            return False

    def execute_fourth_generation(self, page):
        """执行第四轮生成"""
        try:
            print("开始执行第四轮生成...")

            # 步骤1: 等待第三轮生成完成
            if not self.wait_for_generation_complete(page):
                print("❌ 第三轮生成未完成，跳过第四轮")
                return False

            # 步骤2: 额外等待30秒保险
            print("步骤2: 额外等待30秒...")
            time.sleep(30)

            # 步骤3: 点击第四个输入框
            print("步骤3: 查找并点击第四个输入框...")
            fourth_input = self.find_locator_any_frame(page, self.fourth_input_xpath)
            fourth_input.wait_for(state="visible", timeout=15000)
            fourth_input.click()
            print("✅ 成功点击第四个输入框！")

            # 步骤4: 输入第四条命令
            print("步骤4: 输入第四条命令...")
            fourth_input.fill(self.fourth_command)
            print("✅ 成功输入第四条命令！")

            # 步骤5: 点击第四个提交按钮
            print("步骤5: 查找并点击第四个提交按钮...")
            time.sleep(2)  # 等待输入完成
            fourth_submit = self.find_locator_any_frame(page, self.fourth_submit_xpath)
            fourth_submit.wait_for(state="visible", timeout=15000)
            fourth_submit.click(force=True)
            print("✅ 成功点击第四个提交按钮！")

            print("✅ 第四轮生成流程执行完成！")
            return True

        except Exception as e:
            print(f"❌ 第四轮生成执行失败: {e}")
            return False

    def execute_loop_generation(self, page):
        """执行循环优化生成（重复8次）"""
        try:
            print(f"开始执行循环优化生成（共{self.loop_count}次）...")

            for i in range(self.loop_count):
                print(f"\n=== 第{i+1}次循环优化 ===")

                # 步骤1: 等待上一轮生成完成
                if not self.wait_for_generation_complete(page):
                    print(f"❌ 第{i+1}次循环：上一轮生成未完成")
                    continue

                # 步骤2: 等待30秒保险
                print(f"步骤2: 第{i+1}次循环等待30秒...")
                time.sleep(30)

                # 步骤3: 点击输入框
                print(f"步骤3: 第{i+1}次循环点击输入框...")
                loop_input = self.find_locator_any_frame(page, self.loop_input_xpath)
                loop_input.wait_for(state="visible", timeout=15000)
                loop_input.click()
                print(f"✅ 第{i+1}次循环成功点击输入框！")

                # 步骤4: 输入循环命令
                print(f"步骤4: 第{i+1}次循环输入命令...")
                loop_input.fill(self.loop_command)
                print(f"✅ 第{i+1}次循环成功输入命令！")

                # 步骤5: 点击提交按钮
                print(f"步骤5: 第{i+1}次循环点击提交按钮...")
                time.sleep(2)  # 等待输入完成
                loop_submit = self.find_locator_any_frame(page, self.loop_submit_xpath)
                loop_submit.wait_for(state="visible", timeout=15000)
                loop_submit.click(force=True)
                print(f"✅ 第{i+1}次循环成功点击提交按钮！")

                print(f"✅ 第{i+1}次循环优化完成！")

            # 最后一次等待生成完成和30秒
            print("\n=== 最终等待 ===")
            if self.wait_for_generation_complete(page):
                print("✅ 最后一轮生成完成！")
                print("最终等待30秒...")
                time.sleep(30)
                print("✅ 最终等待完成！")
            else:
                print("❌ 最后一轮生成监控超时")

            print("✅ 循环优化生成流程执行完成！")
            return True

        except Exception as e:
            print(f"❌ 循环优化生成执行失败: {e}")
            return False

    def execute_final_operations(self, page):
        """执行最终的操作步骤"""
        try:
            print("开始执行最终操作步骤...")

            # 步骤1: 点击第一个按钮
            print("步骤1: 点击第一个最终按钮...")
            try:
                final_button1 = self.find_locator_any_frame(page, self.final_button1_xpath)
                final_button1.wait_for(state="visible", timeout=15000)
                final_button1.click(force=True)
                print("✅ 成功点击第一个最终按钮！")
                time.sleep(2)

                # 步骤2: 点击第二个按钮
                print("步骤2: 点击第二个最终按钮...")
                final_button2 = self.find_locator_any_frame(page, self.final_button2_xpath)
                final_button2.wait_for(state="visible", timeout=15000)
                final_button2.click(force=True)
                print("✅ 成功点击第二个最终按钮！")
                time.sleep(2)

                # 步骤3: 点击第三个按钮
                print("步骤3: 点击第三个最终按钮...")
                final_button3 = self.find_locator_any_frame(page, self.final_button3_xpath)
                final_button3.wait_for(state="visible", timeout=15000)
                final_button3.click(force=True)
                print("✅ 成功点击第三个最终按钮！")
                time.sleep(2)

            except Exception as e:
                print(f"❌ 第一个按钮点击失败: {e}")
                print("🔄 直接跳转到步骤4...")

            # 步骤4: 点击第四个按钮（无论前面是否成功都会执行）
            print("步骤4: 点击第四个最终按钮...")
            final_button4 = self.find_locator_any_frame(page, self.final_button4_xpath)
            final_button4.wait_for(state="visible", timeout=15000)
            final_button4.click(force=True)
            print("✅ 成功点击第四个最终按钮！")

            # 步骤5: 最终等待30秒
            print("步骤5: 最终等待30秒...")
            time.sleep(30)
            print("✅ 最终等待完成！")

            print("✅ 最终操作步骤执行完成！")
            return True

        except Exception as e:
            print(f"❌ 最终操作步骤执行失败: {e}")
            return False

    def execute_poster_generation_cycle(self, page, row_number):
        """执行完整的海报生成周期（从after_login_click开始）"""
        try:
            print(f"\n{'='*60}")
            print(f"开始第{row_number}轮海报生成周期...")
            print(f"{'='*60}")

            # 步骤1: 点击after_login_click元素
            print("步骤1: 点击登录后的目标元素...")
            after_login_element = self.find_locator_any_frame(page, self.after_login_click_xpath)
            after_login_element.wait_for(state="visible", timeout=15000)
            after_login_element.click(force=True)
            print("✅ 成功点击登录后的目标元素！")

            # 步骤2: 点击海报输入框并输入内容
            print("步骤2: 查找海报输入框...")
            time.sleep(2)  # 等待页面加载
            poster_input = self.find_locator_any_frame(page, self.poster_input_xpath)
            poster_input.wait_for(state="visible", timeout=15000)
            poster_input.click()
            print("✅ 成功点击海报输入框！")

            # 步骤3: 读取Excel数据并生成提示词
            print(f"步骤3: 读取Excel第{row_number}行数据...")
            entity_word = self.read_excel_data(row_number)
            if not entity_word:
                print(f"❌ 无法读取Excel第{row_number}行数据，结束循环")
                return False

            # 步骤4: 生成并输入完整提示词
            print("步骤4: 生成并输入海报提示词...")
            full_prompt = self.generate_poster_prompt(entity_word)
            poster_input.fill(full_prompt)
            print("✅ 成功输入海报提示词！")

            # 步骤5: 点击提交按钮
            print("步骤5: 查找并点击提交按钮...")
            time.sleep(2)  # 等待输入完成
            final_submit_button = self.find_locator_any_frame(page, self.final_submit_button_xpath)
            final_submit_button.wait_for(state="visible", timeout=15000)
            final_submit_button.click(force=True)
            print("✅ 成功点击提交按钮！")

            # 步骤6: 执行第二轮生成
            print("步骤6: 开始第二轮生成流程...")
            if self.execute_second_generation(page):
                print("✅ 第二轮生成流程执行成功！")

                # 步骤7: 执行第三轮生成
                print("步骤7: 开始第三轮生成流程...")
                if self.execute_third_generation(page):
                    print("✅ 第三轮生成流程执行成功！")

                    # 步骤8: 执行第四轮生成
                    print("步骤8: 开始第四轮生成流程...")
                    if self.execute_fourth_generation(page):
                        print("✅ 第四轮生成流程执行成功！")

                        # 步骤9: 执行循环优化生成
                        print("步骤9: 开始循环优化生成流程...")
                        if self.execute_loop_generation(page):
                            print("✅ 循环优化生成流程执行成功！")

                            # 步骤10: 执行最终操作
                            print("步骤10: 开始最终操作流程...")
                            if self.execute_final_operations(page):
                                print("✅ 最终操作流程执行成功！")
                            else:
                                print("❌ 最终操作流程执行失败")
                        else:
                            print("❌ 循环优化生成流程执行失败")
                    else:
                        print("❌ 第四轮生成流程执行失败")
                else:
                    print("❌ 第三轮生成流程执行失败")
            else:
                print("❌ 第二轮生成流程执行失败")

            print(f"✅ 第{row_number}轮海报生成周期执行完成！")
            return True

        except Exception as e:
            print(f"❌ 第{row_number}轮海报生成周期执行失败: {e}")
            return False

    def perform_login_steps(self, page):
        """执行登录流程的步骤"""
        try:
            print("开始执行登录流程...")

            # 步骤1: 等待并点击手机号输入框
            print("步骤1: 查找手机号输入框...")
            phone_input = self.find_locator_any_frame(page, self.phone_input_xpath)
            phone_input.wait_for(state="visible", timeout=10000)
            phone_input.click()
            time.sleep(1)

            # 步骤2: 输入手机号
            print(f"步骤2: 输入手机号 {self.phone_number}...")
            phone_input.fill(self.phone_number)
            time.sleep(1)

            # 步骤3: 点击协议复选框
            print("步骤3: 点击协议复选框...")
            agreement_checkbox = self.find_locator_any_frame(page, self.agreement_checkbox_xpath)
            agreement_checkbox.wait_for(state="visible", timeout=10000)
            agreement_checkbox.click()
            time.sleep(1)

            # 步骤4: 点击登录提交按钮
            print("步骤4: 点击登录提交按钮...")
            login_submit = self.find_locator_any_frame(page, self.login_submit_xpath)
            login_submit.wait_for(state="visible", timeout=10000)
            login_submit.click()
            time.sleep(1)

            # 步骤5: 等待30秒后点击指定元素
            print("步骤5: 等待30秒后点击指定元素...")
            print("等待30秒...")
            time.sleep(30)

            print("查找并点击登录后的目标元素...")
            after_login_element = self.find_locator_any_frame(page, self.after_login_click_xpath)
            after_login_element.wait_for(state="visible", timeout=15000)
            after_login_element.click(force=True)
            print("✅ 成功点击登录后的目标元素！")

            # 步骤6: 点击海报输入框并输入内容
            print("步骤6: 查找海报输入框...")
            time.sleep(2)  # 等待页面加载
            poster_input = self.find_locator_any_frame(page, self.poster_input_xpath)
            poster_input.wait_for(state="visible", timeout=15000)
            poster_input.click()
            print("✅ 成功点击海报输入框！")

            # 步骤7: 读取Excel数据并生成提示词
            print("步骤7: 读取Excel数据...")
            entity_word = self.read_excel_data(1)  # 第一轮使用A1
            if not entity_word:
                print("❌ 无法读取Excel数据，使用默认值")
                entity_word = "默认产品"

            # 步骤8: 生成并输入完整提示词
            print("步骤8: 生成并输入海报提示词...")
            full_prompt = self.generate_poster_prompt(entity_word)
            poster_input.fill(full_prompt)
            print("✅ 成功输入海报提示词！")

            # 步骤9: 点击最终提交按钮
            print("步骤9: 查找并点击最终提交按钮...")
            time.sleep(2)  # 等待输入完成
            final_submit_button = self.find_locator_any_frame(page, self.final_submit_button_xpath)
            final_submit_button.wait_for(state="visible", timeout=15000)
            final_submit_button.click(force=True)
            print("✅ 成功点击最终提交按钮！")

            # 步骤10: 执行第二轮生成
            print("步骤10: 开始第二轮生成流程...")
            if self.execute_second_generation(page):
                print("✅ 第二轮生成流程执行成功！")

                # 步骤11: 执行第三轮生成
                print("步骤11: 开始第三轮生成流程...")
                if self.execute_third_generation(page):
                    print("✅ 第三轮生成流程执行成功！")

                    # 步骤12: 执行第四轮生成
                    print("步骤12: 开始第四轮生成流程...")
                    if self.execute_fourth_generation(page):
                        print("✅ 第四轮生成流程执行成功！")

                        # 步骤13: 执行循环优化生成
                        print("步骤13: 开始循环优化生成流程...")
                        if self.execute_loop_generation(page):
                            print("✅ 循环优化生成流程执行成功！")

                            # 步骤14: 执行最终操作
                            print("步骤14: 开始最终操作流程...")
                            if self.execute_final_operations(page):
                                print("✅ 最终操作流程执行成功！")

                                # 步骤15: 开始循环处理后续Excel行
                                print("步骤15: 开始循环处理后续Excel行...")
                                row_number = 2  # 从第二行开始
                                while True:
                                    print(f"\n🔄 准备处理Excel第{row_number}行...")
                                    # 先检查是否有数据
                                    test_data = self.read_excel_data(row_number)
                                    if not test_data:
                                        print(f"✅ Excel第{row_number}行无数据，循环结束")
                                        break

                                    # 执行完整的海报生成周期
                                    if self.execute_poster_generation_cycle(page, row_number):
                                        print(f"✅ 第{row_number}轮处理完成")
                                    else:
                                        print(f"❌ 第{row_number}轮处理失败")
                                        break

                                    row_number += 1

                                print("✅ 所有Excel数据处理完成！")
                            else:
                                print("❌ 最终操作流程执行失败")
                        else:
                            print("❌ 循环优化生成流程执行失败")
                    else:
                        print("❌ 第四轮生成流程执行失败")
                else:
                    print("❌ 第三轮生成流程执行失败")
            else:
                print("❌ 第二轮生成流程执行失败")

            print("✅ 完整登录和海报生成流程执行完成！")
            return True

        except Exception as e:
            print(f"❌ 登录流程执行失败: {e}")
            return False

    def open_with_playwright(self):
        """使用Playwright打开豆包AI网站并执行自动化操作（参考ZDHTool.py的成功方法）"""
        if not PLAYWRIGHT_AVAILABLE:
            print("❌ Playwright未安装，请运行: pip install playwright && playwright install")
            return False

        try:
            print("正在使用Playwright打开豆包AI网站...")

            with sync_playwright() as p:
                browser = None

                # 优先使用指定的Edge可执行文件，参考ZDHTool.py的方法
                for edge_path in self.edge_paths:
                    try:
                        if Path(edge_path).is_file():
                            print(f"尝试使用Edge路径: {edge_path}")
                            browser = p.chromium.launch(executable_path=edge_path, headless=False)
                            break
                    except Exception as e:
                        print(f"使用路径 {edge_path} 失败: {e}")
                        continue

                # 如果指定路径失败，使用channel方式
                if not browser:
                    try:
                        print("尝试使用channel='msedge'启动...")
                        browser = p.chromium.launch(channel="msedge", headless=False)
                    except Exception as e:
                        print(f"使用channel启动失败: {e}")
                        return False

                page = browser.new_page()
                page.goto(self.url, wait_until="domcontentloaded")

                # 等待页面加载
                print("等待页面加载...")
                time.sleep(3)

                # 查找并点击登录按钮
                print("正在查找登录按钮...")
                loc = self.find_locator_any_frame(page, self.login_button_xpath)

                try:
                    # 等待元素可见
                    print("等待元素可见...")
                    loc.wait_for(state="visible", timeout=20000)
                    print("✅ 元素已可见")
                except Exception as e:
                    print(f"等待元素可见失败: {e}")

                try:
                    # 滚动到元素位置
                    print("滚动到元素位置...")
                    loc.scroll_into_view_if_needed()

                    # 高亮元素（调试用）
                    try:
                        page.evaluate("(xpath) => { const el = document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue; if(el) el.style.border='3px solid red'; }", self.login_button_xpath)
                        time.sleep(1)
                    except:
                        pass

                    # 尝试点击，参考ZDHTool.py的强制点击方法
                    print("尝试点击登录按钮...")
                    loc.click(force=True)
                    print("✅ 成功点击登录按钮！")

                    # 等待登录弹窗出现，然后执行登录流程
                    time.sleep(2)
                    if self.perform_login_steps(page):
                        print("✅ 完整登录流程执行成功！")
                    else:
                        print("❌ 登录流程执行失败")

                except Exception as e:
                    print(f"直接点击失败: {e}，尝试其他方法...")

                    # 方法1: 尝试点击父级按钮（参考ZDHTool.py）
                    try:
                        parent_btn = loc.locator("xpath=ancestor::button[1]")
                        if parent_btn.count():
                            print("尝试点击父级button...")
                            parent_btn.click(force=True)
                            print("✅ 成功点击父级按钮！")
                        else:
                            # 方法2: 使用JavaScript点击父元素
                            print("尝试JavaScript点击父元素...")
                            loc.evaluate("(n) => n.parentElement && n.parentElement.click()")
                            print("✅ JavaScript点击成功！")
                    except Exception as e2:
                        print(f"所有点击方法都失败了: {e2}")
                        return False

                # 等待响应
                time.sleep(3)
                print("✅ Playwright自动化操作完成！")

                # 保持浏览器运行
                print("所有Excel数据处理完成，浏览器将保持运行，按Ctrl+C结束...")
                try:
                    while True:
                        time.sleep(3600)
                except KeyboardInterrupt:
                    print("用户中断，关闭浏览器...")
                    return True

        except Exception as e:
            print(f"Playwright操作失败: {e}")
            return False

    def open_doubao_website(self):
        """使用默认浏览器打开豆包AI网站"""
        try:
            print("正在打开豆包AI网站...")
            print(f"目标网址: {self.url}")
            
            # 使用默认浏览器打开网站
            webbrowser.open(self.url)
            
            print("豆包AI网站已在默认浏览器中打开！")
            print("请在浏览器中查看网站是否正确加载")
            
            return True
            
        except Exception as e:
            print(f"打开豆包AI网站失败: {e}")
            return False
    
    def open_edge_browser(self):
        """尝试使用Edge浏览器打开网站"""
        try:
            print("正在尝试使用Edge浏览器打开豆包AI网站...")
            
            # Windows系统中Edge浏览器的常见路径
            edge_paths = [
                r"C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe",
                r"C:\Program Files\Microsoft\Edge\Application\msedge.exe",
            ]
            
            edge_path = None
            for path in edge_paths:
                if os.path.exists(path):
                    edge_path = path
                    break
            
            if edge_path:
                print(f"找到Edge浏览器: {edge_path}")
                # 注册Edge浏览器
                webbrowser.register('edge', None, webbrowser.BackgroundBrowser(edge_path))
                # 使用Edge打开网站
                webbrowser.get('edge').open(self.url)
                print("豆包AI网站已在Edge浏览器中打开！")
                return True
            else:
                print("未找到Edge浏览器，使用默认浏览器...")
                return self.open_doubao_website()
                
        except Exception as e:
            print(f"使用Edge浏览器失败: {e}")
            print("尝试使用默认浏览器...")
            return self.open_doubao_website()

    def click_element_with_multiple_methods(self, element):
        """使用多种方法尝试点击元素"""
        methods = [
            ("普通点击", lambda el: el.click()),
            ("JavaScript点击", lambda el: self.driver.execute_script("arguments[0].click();", el)),
            ("ActionChains点击", lambda el: self.driver.execute_script("arguments[0].scrollIntoView(true);", el) or time.sleep(0.5) or webdriver.ActionChains(self.driver).move_to_element(el).click().perform()),
            ("强制JavaScript点击", lambda el: self.driver.execute_script("arguments[0].dispatchEvent(new MouseEvent('click', {bubbles: true, cancelable: true, view: window}));", el))
        ]

        for method_name, method_func in methods:
            try:
                print(f"尝试{method_name}...")
                method_func(element)
                print(f"✅ {method_name}成功！")
                return True
            except Exception as e:
                print(f"❌ {method_name}失败: {e}")
                continue

        return False

    def wait_for_generation_complete_selenium(self, timeout=600):
        """使用Selenium等待海报生成完成"""
        print("开始监控生成状态（Selenium）...")
        start_time = time.time()

        while time.time() - start_time < timeout:
            try:
                # 查找状态元素
                status_element = self.wait.until(
                    EC.presence_of_element_located((By.XPATH, self.generation_status_xpath))
                )

                # 获取class属性
                class_value = status_element.get_attribute("class")
                print(f"当前状态class: {class_value}")

                # 检查是否包含 !hidden，表示生成完成
                if "!hidden" in class_value:
                    print("✅ 检测到生成完成标志（!hidden）")
                    return True
                elif "break-btn-Rv8NnA" in class_value and "!hidden" not in class_value:
                    print("🔄 正在生成中...")

                # 每5秒检查一次
                time.sleep(5)

            except Exception as e:
                print(f"监控状态时出错: {e}")
                time.sleep(5)
                continue

        print("❌ 等待生成完成超时")
        return False

    def execute_second_generation_selenium(self):
        """使用Selenium执行第二轮生成"""
        try:
            print("开始执行第二轮生成（Selenium）...")

            # 步骤1: 等待生成完成
            if not self.wait_for_generation_complete_selenium():
                print("❌ 第一轮生成未完成，跳过第二轮")
                return False

            # 步骤2: 额外等待30秒保险
            print("步骤2: 额外等待30秒...")
            time.sleep(30)

            # 步骤3: 点击第二个输入框
            print("步骤3: 查找并点击第二个输入框...")
            second_input = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, self.second_input_xpath))
            )
            second_input.click()
            print("✅ 成功点击第二个输入框！")

            # 步骤4: 输入第二条命令
            print("步骤4: 输入第二条命令...")
            second_input.clear()
            second_input.send_keys(self.second_command)
            print("✅ 成功输入第二条命令！")

            # 步骤5: 点击第二个提交按钮
            print("步骤5: 查找并点击第二个提交按钮...")
            time.sleep(2)  # 等待输入完成
            second_submit = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, self.second_submit_xpath))
            )
            # 滚动到按钮位置
            self.driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", second_submit)
            time.sleep(1)
            # 尝试点击
            if self.click_element_with_multiple_methods(second_submit):
                print("✅ 成功点击第二个提交按钮！")
            else:
                print("❌ 点击第二个提交按钮失败")
                return False

            print("✅ 第二轮生成流程执行完成（Selenium）！")
            return True

        except Exception as e:
            print(f"❌ 第二轮生成执行失败: {e}")
            return False

    def execute_third_generation_selenium(self):
        """使用Selenium执行第三轮生成"""
        try:
            print("开始执行第三轮生成（Selenium）...")

            # 步骤1: 等待第二轮生成完成
            if not self.wait_for_generation_complete_selenium():
                print("❌ 第二轮生成未完成，跳过第三轮")
                return False

            # 步骤2: 额外等待30秒保险
            print("步骤2: 额外等待30秒...")
            time.sleep(30)

            # 步骤3: 点击第三个输入框
            print("步骤3: 查找并点击第三个输入框...")
            third_input = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, self.third_input_xpath))
            )
            third_input.click()
            print("✅ 成功点击第三个输入框！")

            # 步骤4: 输入第三条命令
            print("步骤4: 输入第三条命令...")
            third_input.clear()
            third_input.send_keys(self.third_command)
            print("✅ 成功输入第三条命令！")

            # 步骤5: 点击第三个提交按钮
            print("步骤5: 查找并点击第三个提交按钮...")
            time.sleep(2)  # 等待输入完成
            third_submit = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, self.third_submit_xpath))
            )
            # 滚动到按钮位置
            self.driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", third_submit)
            time.sleep(1)
            # 尝试点击
            if self.click_element_with_multiple_methods(third_submit):
                print("✅ 成功点击第三个提交按钮！")
            else:
                print("❌ 点击第三个提交按钮失败")
                return False

            print("✅ 第三轮生成流程执行完成（Selenium）！")
            return True

        except Exception as e:
            print(f"❌ 第三轮生成执行失败: {e}")
            return False

    def execute_fourth_generation_selenium(self):
        """使用Selenium执行第四轮生成"""
        try:
            print("开始执行第四轮生成（Selenium）...")

            # 步骤1: 等待第三轮生成完成
            if not self.wait_for_generation_complete_selenium():
                print("❌ 第三轮生成未完成，跳过第四轮")
                return False

            # 步骤2: 额外等待30秒保险
            print("步骤2: 额外等待30秒...")
            time.sleep(30)

            # 步骤3: 点击第四个输入框
            print("步骤3: 查找并点击第四个输入框...")
            fourth_input = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, self.fourth_input_xpath))
            )
            fourth_input.click()
            print("✅ 成功点击第四个输入框！")

            # 步骤4: 输入第四条命令
            print("步骤4: 输入第四条命令...")
            fourth_input.clear()
            fourth_input.send_keys(self.fourth_command)
            print("✅ 成功输入第四条命令！")

            # 步骤5: 点击第四个提交按钮
            print("步骤5: 查找并点击第四个提交按钮...")
            time.sleep(2)  # 等待输入完成
            fourth_submit = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, self.fourth_submit_xpath))
            )
            # 滚动到按钮位置
            self.driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", fourth_submit)
            time.sleep(1)
            # 尝试点击
            if self.click_element_with_multiple_methods(fourth_submit):
                print("✅ 成功点击第四个提交按钮！")
            else:
                print("❌ 点击第四个提交按钮失败")
                return False

            print("✅ 第四轮生成流程执行完成（Selenium）！")
            return True

        except Exception as e:
            print(f"❌ 第四轮生成执行失败: {e}")
            return False

    def execute_loop_generation_selenium(self):
        """使用Selenium执行循环优化生成（重复8次）"""
        try:
            print(f"开始执行循环优化生成（Selenium，共{self.loop_count}次）...")

            for i in range(self.loop_count):
                print(f"\n=== 第{i+1}次循环优化（Selenium） ===")

                # 步骤1: 等待上一轮生成完成
                if not self.wait_for_generation_complete_selenium():
                    print(f"❌ 第{i+1}次循环：上一轮生成未完成")
                    continue

                # 步骤2: 等待30秒保险
                print(f"步骤2: 第{i+1}次循环等待30秒...")
                time.sleep(30)

                # 步骤3: 点击输入框
                print(f"步骤3: 第{i+1}次循环点击输入框...")
                loop_input = self.wait.until(
                    EC.element_to_be_clickable((By.XPATH, self.loop_input_xpath))
                )
                loop_input.click()
                print(f"✅ 第{i+1}次循环成功点击输入框！")

                # 步骤4: 输入循环命令
                print(f"步骤4: 第{i+1}次循环输入命令...")
                loop_input.clear()
                loop_input.send_keys(self.loop_command)
                print(f"✅ 第{i+1}次循环成功输入命令！")

                # 步骤5: 点击提交按钮
                print(f"步骤5: 第{i+1}次循环点击提交按钮...")
                time.sleep(2)  # 等待输入完成
                loop_submit = self.wait.until(
                    EC.element_to_be_clickable((By.XPATH, self.loop_submit_xpath))
                )
                # 滚动到按钮位置
                self.driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", loop_submit)
                time.sleep(1)
                # 尝试点击
                if self.click_element_with_multiple_methods(loop_submit):
                    print(f"✅ 第{i+1}次循环成功点击提交按钮！")
                else:
                    print(f"❌ 第{i+1}次循环点击提交按钮失败")
                    continue

                print(f"✅ 第{i+1}次循环优化完成！")

            # 最后一次等待生成完成和30秒
            print("\n=== 最终等待（Selenium） ===")
            if self.wait_for_generation_complete_selenium():
                print("✅ 最后一轮生成完成！")
                print("最终等待30秒...")
                time.sleep(30)
                print("✅ 最终等待完成！")
            else:
                print("❌ 最后一轮生成监控超时")

            print("✅ 循环优化生成流程执行完成（Selenium）！")
            return True

        except Exception as e:
            print(f"❌ 循环优化生成执行失败: {e}")
            return False

    def execute_final_operations_selenium(self):
        """使用Selenium执行最终的操作步骤"""
        try:
            print("开始执行最终操作步骤（Selenium）...")

            # 步骤1: 点击第一个按钮
            print("步骤1: 点击第一个最终按钮...")
            try:
                final_button1 = self.wait.until(
                    EC.element_to_be_clickable((By.XPATH, self.final_button1_xpath))
                )
                self.driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", final_button1)
                time.sleep(1)
                if self.click_element_with_multiple_methods(final_button1):
                    print("✅ 成功点击第一个最终按钮！")
                else:
                    print("❌ 点击第一个最终按钮失败")
                    raise Exception("第一个按钮点击失败")
                time.sleep(2)

                # 步骤2: 点击第二个按钮
                print("步骤2: 点击第二个最终按钮...")
                final_button2 = self.wait.until(
                    EC.element_to_be_clickable((By.XPATH, self.final_button2_xpath))
                )
                self.driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", final_button2)
                time.sleep(1)
                if self.click_element_with_multiple_methods(final_button2):
                    print("✅ 成功点击第二个最终按钮！")
                else:
                    print("❌ 点击第二个最终按钮失败")
                    raise Exception("第二个按钮点击失败")
                time.sleep(2)

                # 步骤3: 点击第三个按钮
                print("步骤3: 点击第三个最终按钮...")
                final_button3 = self.wait.until(
                    EC.element_to_be_clickable((By.XPATH, self.final_button3_xpath))
                )
                self.driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", final_button3)
                time.sleep(1)
                if self.click_element_with_multiple_methods(final_button3):
                    print("✅ 成功点击第三个最终按钮！")
                else:
                    print("❌ 点击第三个最终按钮失败")
                    raise Exception("第三个按钮点击失败")
                time.sleep(2)

            except Exception as e:
                print(f"❌ 前面按钮点击失败: {e}")
                print("🔄 直接跳转到步骤4...")

            # 步骤4: 点击第四个按钮（无论前面是否成功都会执行）
            print("步骤4: 点击第四个最终按钮...")
            final_button4 = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, self.final_button4_xpath))
            )
            self.driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", final_button4)
            time.sleep(1)
            if self.click_element_with_multiple_methods(final_button4):
                print("✅ 成功点击第四个最终按钮！")
            else:
                print("❌ 点击第四个最终按钮失败")
                return False

            # 步骤5: 最终等待30秒
            print("步骤5: 最终等待30秒...")
            time.sleep(30)
            print("✅ 最终等待完成！")

            print("✅ 最终操作步骤执行完成（Selenium）！")
            return True

        except Exception as e:
            print(f"❌ 最终操作步骤执行失败: {e}")
            return False

    def execute_poster_generation_cycle_selenium(self, row_number):
        """使用Selenium执行完整的海报生成周期（从after_login_click开始）"""
        try:
            print(f"\n{'='*60}")
            print(f"开始第{row_number}轮海报生成周期（Selenium）...")
            print(f"{'='*60}")

            # 步骤1: 点击after_login_click元素
            print("步骤1: 点击登录后的目标元素...")
            after_login_element = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, self.after_login_click_xpath))
            )
            self.driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", after_login_element)
            time.sleep(1)
            if self.click_element_with_multiple_methods(after_login_element):
                print("✅ 成功点击登录后的目标元素！")
            else:
                print("❌ 点击登录后的目标元素失败")
                return False

            # 步骤2: 点击海报输入框并输入内容
            print("步骤2: 查找海报输入框...")
            time.sleep(2)  # 等待页面加载
            poster_input = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, self.poster_input_xpath))
            )
            poster_input.click()
            print("✅ 成功点击海报输入框！")

            # 步骤3: 读取Excel数据并生成提示词
            print(f"步骤3: 读取Excel第{row_number}行数据...")
            entity_word = self.read_excel_data(row_number)
            if not entity_word:
                print(f"❌ 无法读取Excel第{row_number}行数据，结束循环")
                return False

            # 步骤4: 生成并输入完整提示词
            print("步骤4: 生成并输入海报提示词...")
            full_prompt = self.generate_poster_prompt(entity_word)
            poster_input.clear()
            poster_input.send_keys(full_prompt)
            print("✅ 成功输入海报提示词！")

            # 步骤5: 点击提交按钮
            print("步骤5: 查找并点击提交按钮...")
            time.sleep(2)  # 等待输入完成
            final_submit_button = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, self.final_submit_button_xpath))
            )
            self.driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", final_submit_button)
            time.sleep(1)
            if self.click_element_with_multiple_methods(final_submit_button):
                print("✅ 成功点击提交按钮！")
            else:
                print("❌ 点击提交按钮失败")
                return False

            # 步骤6: 执行第二轮生成
            print("步骤6: 开始第二轮生成流程...")
            if self.execute_second_generation_selenium():
                print("✅ 第二轮生成流程执行成功！")

                # 步骤7: 执行第三轮生成
                print("步骤7: 开始第三轮生成流程...")
                if self.execute_third_generation_selenium():
                    print("✅ 第三轮生成流程执行成功！")

                    # 步骤8: 执行第四轮生成
                    print("步骤8: 开始第四轮生成流程...")
                    if self.execute_fourth_generation_selenium():
                        print("✅ 第四轮生成流程执行成功！")

                        # 步骤9: 执行循环优化生成
                        print("步骤9: 开始循环优化生成流程...")
                        if self.execute_loop_generation_selenium():
                            print("✅ 循环优化生成流程执行成功！")

                            # 步骤10: 执行最终操作
                            print("步骤10: 开始最终操作流程...")
                            if self.execute_final_operations_selenium():
                                print("✅ 最终操作流程执行成功！")
                            else:
                                print("❌ 最终操作流程执行失败")
                        else:
                            print("❌ 循环优化生成流程执行失败")
                    else:
                        print("❌ 第四轮生成流程执行失败")
                else:
                    print("❌ 第三轮生成流程执行失败")
            else:
                print("❌ 第二轮生成流程执行失败")

            print(f"✅ 第{row_number}轮海报生成周期执行完成（Selenium）！")
            return True

        except Exception as e:
            print(f"❌ 第{row_number}轮海报生成周期执行失败: {e}")
            return False

    def perform_login_steps_selenium(self):
        """使用Selenium执行登录流程的步骤"""
        try:
            print("开始执行Selenium登录流程...")

            # 步骤1: 等待并点击手机号输入框
            print("步骤1: 查找手机号输入框...")
            phone_input = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, self.phone_input_xpath))
            )
            phone_input.click()
            time.sleep(1)

            # 步骤2: 输入手机号
            print(f"步骤2: 输入手机号 {self.phone_number}...")
            phone_input.clear()
            phone_input.send_keys(self.phone_number)
            time.sleep(1)

            # 步骤3: 点击协议复选框
            print("步骤3: 点击协议复选框...")
            agreement_checkbox = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, self.agreement_checkbox_xpath))
            )
            agreement_checkbox.click()
            time.sleep(1)

            # 步骤4: 点击登录提交按钮
            print("步骤4: 点击登录提交按钮...")
            login_submit = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, self.login_submit_xpath))
            )
            login_submit.click()
            time.sleep(1)

            # 步骤5: 等待30秒后点击指定元素
            print("步骤5: 等待30秒后点击指定元素...")
            print("等待30秒...")
            time.sleep(30)

            print("查找并点击登录后的目标元素...")
            after_login_element = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, self.after_login_click_xpath))
            )
            # 滚动到元素位置
            self.driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", after_login_element)
            time.sleep(1)
            # 尝试点击
            if self.click_element_with_multiple_methods(after_login_element):
                print("✅ 成功点击登录后的目标元素！")
            else:
                print("❌ 点击登录后的目标元素失败")
                return False

            # 步骤6: 点击海报输入框并输入内容
            print("步骤6: 查找海报输入框...")
            time.sleep(2)  # 等待页面加载
            poster_input = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, self.poster_input_xpath))
            )
            poster_input.click()
            print("✅ 成功点击海报输入框！")

            # 步骤7: 读取Excel数据并生成提示词
            print("步骤7: 读取Excel数据...")
            entity_word = self.read_excel_data(1)  # 第一轮使用A1
            if not entity_word:
                print("❌ 无法读取Excel数据，使用默认值")
                entity_word = "默认产品"

            # 步骤8: 生成并输入完整提示词
            print("步骤8: 生成并输入海报提示词...")
            full_prompt = self.generate_poster_prompt(entity_word)
            poster_input.clear()
            poster_input.send_keys(full_prompt)
            print("✅ 成功输入海报提示词！")

            # 步骤9: 点击最终提交按钮
            print("步骤9: 查找并点击最终提交按钮...")
            time.sleep(2)  # 等待输入完成
            final_submit_button = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, self.final_submit_button_xpath))
            )
            # 滚动到按钮位置
            self.driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", final_submit_button)
            time.sleep(1)
            # 尝试点击
            if self.click_element_with_multiple_methods(final_submit_button):
                print("✅ 成功点击最终提交按钮！")
            else:
                print("❌ 点击最终提交按钮失败")
                return False

            # 步骤10: 执行第二轮生成
            print("步骤10: 开始第二轮生成流程...")
            if self.execute_second_generation_selenium():
                print("✅ 第二轮生成流程执行成功！")

                # 步骤11: 执行第三轮生成
                print("步骤11: 开始第三轮生成流程...")
                if self.execute_third_generation_selenium():
                    print("✅ 第三轮生成流程执行成功！")

                    # 步骤12: 执行第四轮生成
                    print("步骤12: 开始第四轮生成流程...")
                    if self.execute_fourth_generation_selenium():
                        print("✅ 第四轮生成流程执行成功！")

                        # 步骤13: 执行循环优化生成
                        print("步骤13: 开始循环优化生成流程...")
                        if self.execute_loop_generation_selenium():
                            print("✅ 循环优化生成流程执行成功！")

                            # 步骤14: 执行最终操作
                            print("步骤14: 开始最终操作流程...")
                            if self.execute_final_operations_selenium():
                                print("✅ 最终操作流程执行成功！")

                                # 步骤15: 开始循环处理后续Excel行
                                print("步骤15: 开始循环处理后续Excel行...")
                                row_number = 2  # 从第二行开始
                                while True:
                                    print(f"\n🔄 准备处理Excel第{row_number}行...")
                                    # 先检查是否有数据
                                    test_data = self.read_excel_data(row_number)
                                    if not test_data:
                                        print(f"✅ Excel第{row_number}行无数据，循环结束")
                                        break

                                    # 执行完整的海报生成周期
                                    if self.execute_poster_generation_cycle_selenium(row_number):
                                        print(f"✅ 第{row_number}轮处理完成")
                                    else:
                                        print(f"❌ 第{row_number}轮处理失败")
                                        break

                                    row_number += 1

                                print("✅ 所有Excel数据处理完成！")
                            else:
                                print("❌ 最终操作流程执行失败")
                        else:
                            print("❌ 循环优化生成流程执行失败")
                    else:
                        print("❌ 第四轮生成流程执行失败")
                else:
                    print("❌ 第三轮生成流程执行失败")
            else:
                print("❌ 第二轮生成流程执行失败")

            print("✅ 完整Selenium登录和海报生成流程执行完成！")
            return True

        except Exception as e:
            print(f"❌ Selenium登录流程执行失败: {e}")
            return False

    def open_with_selenium(self):
        """使用Selenium打开豆包AI网站并执行自动化操作"""
        try:
            if not self.driver:
                print("Selenium驱动未初始化")
                return False

            print("正在使用Selenium打开豆包AI网站...")
            self.driver.get(self.url)

            print("网站已打开，等待15秒让页面完全加载...")
            time.sleep(15)

            print("正在查找登录按钮...")
            try:
                # 首先尝试等待元素存在
                print("等待元素存在...")
                element = self.wait.until(
                    EC.presence_of_element_located((By.XPATH, self.login_button_xpath))
                )
                print("✅ 元素已找到")

                # 滚动到元素位置
                print("滚动到元素位置...")
                self.driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", element)
                time.sleep(2)

                # 等待元素可见
                print("等待元素可见...")
                self.wait.until(EC.visibility_of(element))
                print("✅ 元素已可见")

                # 等待元素可点击
                print("等待元素可点击...")
                clickable_element = self.wait.until(
                    EC.element_to_be_clickable((By.XPATH, self.login_button_xpath))
                )
                print("✅ 元素已可点击")

                # 高亮显示元素（用于调试）
                self.driver.execute_script("arguments[0].style.border='3px solid red';", clickable_element)
                time.sleep(1)

                print("正在尝试点击登录按钮...")
                if self.click_element_with_multiple_methods(clickable_element):
                    print("✅ 成功点击登录按钮！")
                    time.sleep(3)  # 等待登录弹窗出现

                    # 执行登录流程
                    if self.perform_login_steps_selenium():
                        print("✅ 完整登录流程执行成功！")
                        return True
                    else:
                        print("❌ 登录流程执行失败")
                        return False
                else:
                    print("❌ 所有点击方法都失败了")
                    return False

            except TimeoutException:
                print("❌ 未找到登录按钮（超时）")
                print("可能页面结构已改变或加载时间过长")
                # 打印当前页面源码的一部分用于调试
                print("当前页面标题:", self.driver.title)
                return False
            except Exception as e:
                print(f"❌ 查找或点击登录按钮时发生错误: {e}")
                return False

        except Exception as e:
            print(f"使用Selenium操作时发生错误: {e}")
            return False

    def close_browser(self):
        """关闭浏览器"""
        if self.driver:
            self.driver.quit()
            print("浏览器已关闭")

    def wait_for_user_input(self):
        """等待用户输入下一步操作"""
        print("\n" + "="*50)
        print("第一步完成！浏览器已打开豆包AI网站")
        print("请在浏览器中确认网站已正确加载")
        print("然后告诉我接下来需要执行的操作...")
        print("="*50)
        
        # 等待用户指示
        input("按Enter键继续或Ctrl+C退出...")


def main():
    """主函数"""
    print("开始启动豆包AI自动化脚本...")
    print("检查可用的自动化工具...")

    if PLAYWRIGHT_AVAILABLE:
        print("✅ Playwright可用")
    else:
        print("❌ Playwright不可用")

    if SELENIUM_AVAILABLE:
        print("✅ Selenium可用")
    else:
        print("❌ Selenium不可用")

    print(f"📊 Excel读取状态:")
    excel_available = check_excel_libraries()
    print(f"   - Pandas可用: {'✅' if PANDAS_AVAILABLE else '❌'}")
    print(f"   - Openpyxl可用: {'✅' if OPENPYXL_AVAILABLE else '❌'}")
    if not excel_available:
        print("   - 将在需要时尝试自动安装Excel读取库")

    automation = DoubaoSimpleAutomation()

    try:
        # 优先使用Playwright（参考ZDHTool.py的成功经验）
        if PLAYWRIGHT_AVAILABLE:
            print("\n使用Playwright模式进行自动化操作...")
            if automation.open_with_playwright():
                print("\n" + "="*50)
                print("✅ Playwright自动化操作完成！")
                print("- 已打开豆包AI网站")
                print("- 已成功点击登录按钮")
                print("- 已完成登录流程（输入手机号、勾选协议、提交）")
                print("- 已等待30秒并点击登录后的目标元素")
                print("- 已读取Excel文件并生成海报提示词")
                print("- 已输入完整的海报生成指令")
                print("- 已点击最终提交按钮")
                print("- 已监控第一轮生成完成")
                print("- 已执行第二轮优化生成")
                print("- 已监控第二轮生成完成")
                print("- 已执行第三轮深度优化生成")
                print("- 已监控第三轮生成完成")
                print("- 已执行第四轮终极优化生成")
                print("- 已监控第四轮生成完成")
                print(f"- 已执行循环优化生成（共{automation.loop_count}次）")
                print("- 已监控最终生成完成")
                print("- 已执行最终操作步骤（4个按钮点击）")
                print("- 已完成最终等待30秒")
                print("- 已循环处理所有Excel数据行")
                print("- 浏览器保持运行状态")
                print("="*50)
                return
            else:
                print("❌ Playwright操作失败，尝试Selenium...")

        # 备用方案：使用Selenium
        if SELENIUM_AVAILABLE and automation.setup_selenium_driver():
            print("使用Selenium模式进行自动化操作...")
            if automation.open_with_selenium():
                print("\n" + "="*50)
                print("✅ Selenium自动化操作完成！")
                print("- 已打开豆包AI网站")
                print("- 已点击登录按钮")
                print("- 已完成登录流程（输入手机号、勾选协议、提交）")
                print("- 已等待30秒并点击登录后的目标元素")
                print("- 已读取Excel文件并生成海报提示词")
                print("- 已输入完整的海报生成指令")
                print("- 已点击最终提交按钮")
                print("- 已监控第一轮生成完成")
                print("- 已执行第二轮优化生成")
                print("- 已监控第二轮生成完成")
                print("- 已执行第三轮深度优化生成")
                print("- 已监控第三轮生成完成")
                print("- 已执行第四轮终极优化生成")
                print("- 已监控第四轮生成完成")
                print(f"- 已执行循环优化生成（共{automation.loop_count}次）")
                print("- 已监控最终生成完成")
                print("- 已执行最终操作步骤（4个按钮点击）")
                print("- 已完成最终等待30秒")
                print("- 已循环处理所有Excel数据行")
                print("- 浏览器保持运行状态")
                print("="*50)
            else:
                print("❌ Selenium自动化操作失败")
        else:
            print("所有自动化工具都不可用，尝试简单模式...")
            # 最后的备用方案：简单浏览器打开
            if automation.open_edge_browser():
                print("请手动在浏览器中点击登录按钮")
            else:
                print("所有方法都失败了")
                return

        # 等待用户输入下一步操作（仅在非Playwright模式下）
        automation.wait_for_user_input()

    except KeyboardInterrupt:
        print("\n用户中断操作")
    except Exception as e:
        print(f"发生错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        if hasattr(automation, 'driver') and automation.driver:
            automation.close_browser()


if __name__ == "__main__":
    main()
