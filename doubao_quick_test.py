"""
豆包AI图像生成按钮快速测试脚本
专门用于快速验证优化后的定位策略
"""

import time
from selenium import webdriver
from selenium.webdriver.edge.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC


def setup_driver():
    """快速设置浏览器驱动"""
    try:
        edge_options = Options()
        edge_options.add_argument("--disable-blink-features=AutomationControlled")
        edge_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        
        driver = webdriver.Edge(options=edge_options)
        driver.maximize_window()
        return driver
    except Exception as e:
        print(f"设置驱动失败: {e}")
        return None


def quick_find_image_button(driver):
    """快速查找图像生成按钮"""
    print("快速查找图像生成按钮...")
    
    # 最有可能的选择器（基于豆包网站的实际结构）
    priority_selectors = [
        "//div[@data-testid='skill_bar_button_3']",
        "//div[@data-testid='skill_bar_button_2']", 
        "(//div[contains(@class, 'skill-bar-button')])[3]",
        "(//div[contains(@class, 'skill-bar-button')])[4]",
        "//div[contains(@class, 'skill-bar-item-wrap') and position()=3]//div[contains(@class, 'skill-bar-button')]",
        "//div[contains(@class, 'skill-bar-item-wrap') and position()=4]//div[contains(@class, 'skill-bar-button')]"
    ]
    
    wait = WebDriverWait(driver, 10)
    
    for i, selector in enumerate(priority_selectors, 1):
        try:
            print(f"尝试选择器 {i}: {selector}")
            
            # 查找元素
            elements = driver.find_elements(By.XPATH, selector)
            if not elements:
                print(f"  未找到元素")
                continue
            
            print(f"  找到 {len(elements)} 个元素")
            
            for j, element in enumerate(elements):
                try:
                    if not element.is_displayed() or not element.is_enabled():
                        continue
                    
                    text = element.text.strip()
                    class_name = element.get_attribute('class') or ''
                    testid = element.get_attribute('data-testid') or ''
                    
                    print(f"    元素 {j+1}: 文本='{text}', testid='{testid}'")
                    
                    # 滚动到元素
                    driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", element)
                    time.sleep(1)
                    
                    # 尝试点击
                    print(f"    尝试点击元素 {j+1}...")
                    element.click()
                    time.sleep(3)
                    
                    # 检查是否成功
                    current_url = driver.current_url
                    print(f"    点击后URL: {current_url}")
                    
                    # 检查是否出现图像生成界面
                    success_indicators = [
                        "//input[@placeholder and contains(@placeholder, '描述')]",
                        "//textarea[@placeholder and contains(@placeholder, '描述')]",
                        "//div[contains(text(), '图像生成')]",
                        "//div[contains(text(), '生成图像')]"
                    ]
                    
                    for indicator in success_indicators:
                        try:
                            found_elements = driver.find_elements(By.XPATH, indicator)
                            if found_elements:
                                print(f"    ✅ 成功！找到图像生成界面: {indicator}")
                                return True
                        except:
                            continue
                    
                    print(f"    ⚠️ 点击了但未检测到图像生成界面")
                    
                except Exception as e:
                    print(f"    元素 {j+1} 点击失败: {e}")
                    continue
                    
        except Exception as e:
            print(f"选择器 {i} 失败: {e}")
            continue
    
    return False


def main():
    """主函数"""
    print("豆包AI图像生成按钮快速测试")
    print("="*40)
    
    driver = setup_driver()
    if not driver:
        return
    
    try:
        # 打开豆包网站
        print("打开豆包AI网站...")
        driver.get("https://www.doubao.com")
        
        # 等待页面加载
        print("等待页面加载...")
        time.sleep(8)
        
        # 快速查找并点击图像生成按钮
        success = quick_find_image_button(driver)
        
        if success:
            print("\n✅ 测试成功！找到并点击了图像生成按钮")
        else:
            print("\n❌ 测试失败！未能找到或点击图像生成按钮")
            
            # 显示当前页面的一些信息帮助调试
            print("\n调试信息:")
            print(f"当前URL: {driver.current_url}")
            print(f"页面标题: {driver.title}")
            
            # 查找所有data-testid属性
            testid_elements = driver.find_elements(By.XPATH, "//div[@data-testid]")
            print(f"找到 {len(testid_elements)} 个带data-testid的元素:")
            for i, elem in enumerate(testid_elements[:10]):  # 只显示前10个
                try:
                    testid = elem.get_attribute('data-testid')
                    text = elem.text.strip()[:20]
                    print(f"  {i+1}. data-testid='{testid}', 文本='{text}'")
                except:
                    continue
        
        # 保持浏览器打开以便查看
        input("\n按Enter键关闭浏览器...")
        
    except Exception as e:
        print(f"发生错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        driver.quit()


if __name__ == "__main__":
    main()
