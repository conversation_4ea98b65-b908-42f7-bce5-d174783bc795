import time
from pynput import mouse
from pynput.mouse import <PERSON><PERSON>

def test_drag():
    """简单的拖拽测试"""
    mouse_controller = mouse.Controller()
    
    print("3秒后开始拖拽测试...")
    time.sleep(3)
    
    # 获取当前位置
    start_pos = mouse_controller.position
    print(f"开始位置: {start_pos}")
    
    # 按下鼠标左键
    mouse_controller.press(Button.left)
    print("鼠标左键按下")
    
    # 慢慢移动鼠标
    for i in range(50):
        current_pos = mouse_controller.position
        new_x = current_pos[0] + 2
        new_y = current_pos[1] + 1
        mouse_controller.position = (new_x, new_y)
        time.sleep(0.1)
        if i % 10 == 0:
            print(f"移动到: ({new_x}, {new_y})")
    
    # 释放鼠标左键
    mouse_controller.release(Button.left)
    end_pos = mouse_controller.position
    print(f"结束位置: {end_pos}")
    print("拖拽测试完成")

if __name__ == "__main__":
    test_drag()
